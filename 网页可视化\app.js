// 全局变量
let 当前股票代码 = '';
let 股票列表 = [];
let 图表实例 = {};

// API基础URL
const API_BASE_URL = 'http://127.0.0.1:5000/api';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    初始化应用();
});

// 初始化应用
async function 初始化应用() {
    显示加载中();
    
    try {
        // 设置标签页切换事件
        设置标签页切换();

        // 设置日期默认值
        设置默认日期();

        // 设置表格排序事件
        设置表格排序事件();

        // 加载股票列表
        await 加载股票列表();

        // 加载市场概览数据
        await 刷新市场概览();

        // 加载数据库状态
        await 刷新数据库状态();

        // 加载可用交易日期
        await 加载可用交易日期();

        // 加载所有股票数据（用于数据详情页面）
        await 加载所有股票数据();
        
        隐藏加载中();
        
    } catch (error) {
        console.error('初始化失败:', error);
        显示错误提示('应用初始化失败: ' + error.message);
        隐藏加载中();
    }
}

// 设置标签页切换
function 设置标签页切换() {
    const 标签按钮 = document.querySelectorAll('.nav-btn');
    const 标签内容 = document.querySelectorAll('.tab-content');
    
    标签按钮.forEach(按钮 => {
        按钮.addEventListener('click', function() {
            const 目标标签 = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            标签按钮.forEach(btn => btn.classList.remove('active'));
            标签内容.forEach(content => content.classList.remove('active'));
            
            // 设置当前活动状态
            this.classList.add('active');
            document.getElementById(目标标签 + '-tab').classList.add('active');
        });
    });
}

// 设置默认日期
function 设置默认日期() {
    const 今天 = new Date();
    const 一年前 = new Date(今天.getFullYear() - 1, 今天.getMonth(), 今天.getDate());

    // 个股分析页面的日期
    document.getElementById('start-date').value = 一年前.toISOString().split('T')[0];
    document.getElementById('end-date').value = 今天.toISOString().split('T')[0];

    // 数据详情页面不再需要日期选择器
}

// 设置表格排序事件
function 设置表格排序事件() {
    // 为所有可排序的表头添加点击事件
    document.querySelectorAll('.simple-table th.sortable').forEach(表头 => {
        表头.addEventListener('click', function() {
            const 字段 = this.getAttribute('data-column');
            表格排序(字段);
        });
    });
}

// 加载股票列表
async function 加载股票列表() {
    try {
        const response = await axios.get(`${API_BASE_URL}/stocks`);
        
        if (response.data.success) {
            股票列表 = response.data.data;

            // 填充个股分析页面的股票选择器
            const 选择器 = document.getElementById('stock-selector');
            选择器.innerHTML = '<option value="">选择股票...</option>';

            股票列表.forEach(股票 => {
                // 个股分析页面选项
                const option1 = document.createElement('option');
                option1.value = 股票.code;
                option1.textContent = `${股票.name} (${股票.code})`;
                选择器.appendChild(option1);
            });

            console.log(`加载了 ${股票列表.length} 只股票`);
        } else {
            throw new Error(response.data.error);
        }
    } catch (error) {
        console.error('加载股票列表失败:', error);
        显示错误提示('加载股票列表失败');
    }
}

// 刷新市场概览
async function 刷新市场概览() {
    显示加载中();
    
    try {
        const response = await axios.get(`${API_BASE_URL}/market/overview`);
        
        if (response.data.success) {
            const 数据 = response.data.data;
            
            // 更新统计卡片
            更新统计卡片(数据.summary);
            
            // 绘制图表
            绘制涨跌分布图(数据.distribution);
            绘制成交额排行图(数据.topAmount);
            绘制行业涨跌幅图(数据.industries);
            绘制行业成交额图(数据.industries);
            
        } else {
            throw new Error(response.data.error);
        }
        
        隐藏加载中();
        
    } catch (error) {
        console.error('刷新市场概览失败:', error);
        显示错误提示('刷新市场概览失败');
        隐藏加载中();
    }
}

// 更新统计卡片
function 更新统计卡片(统计数据) {
    document.getElementById('total-stocks').textContent = 统计数据.totalStocks.toLocaleString();
    document.getElementById('up-stocks').textContent = 统计数据.upStocks.toLocaleString();
    document.getElementById('down-stocks').textContent = 统计数据.downStocks.toLocaleString();
    document.getElementById('avg-change').textContent = 统计数据.avgChange.toFixed(2) + '%';
    document.getElementById('total-amount').textContent = (统计数据.totalAmount / 100000000).toFixed(2) + '亿';
    
    // 设置涨跌颜色
    const 平均涨跌幅元素 = document.getElementById('avg-change');
    平均涨跌幅元素.style.color = 统计数据.avgChange >= 0 ? '#4CAF50' : '#f44336';
}

// 绘制涨跌分布图
function 绘制涨跌分布图(分布数据) {
    const 图表容器 = document.getElementById('distribution-chart');
    
    if (图表实例.分布图) {
        图表实例.分布图.dispose();
    }
    
    图表实例.分布图 = echarts.init(图表容器);
    
    const 数据 = Object.entries(分布数据).map(([名称, 数值]) => ({
        name: 名称,
        value: 数值
    }));
    
    const 配置 = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [{
            name: '涨跌分布',
            type: 'pie',
            radius: '50%',
            data: 数据,
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            itemStyle: {
                color: function(params) {
                    const 颜色映射 = {
                        '涨超5%': '#1B5E20',
                        '涨2-5%': '#4CAF50',
                        '涨0-2%': '#8BC34A',
                        '平盘': '#9E9E9E',
                        '跌0-2%': '#FFCDD2',
                        '跌2-5%': '#F44336',
                        '跌超5%': '#B71C1C'
                    };
                    return 颜色映射[params.name] || '#666';
                }
            }
        }]
    };
    
    图表实例.分布图.setOption(配置);
}

// 绘制成交额排行图
function 绘制成交额排行图(排行数据) {
    const 图表容器 = document.getElementById('top-amount-chart');
    
    if (图表实例.成交额图) {
        图表实例.成交额图.dispose();
    }
    
    图表实例.成交额图 = echarts.init(图表容器);
    
    const 股票名称 = 排行数据.map(item => item.name);
    const 成交额数据 = 排行数据.map(item => (item.amount / 100000000).toFixed(2));
    const 涨跌幅数据 = 排行数据.map(item => item.pctChange);
    
    const 配置 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                const 索引 = params[0].dataIndex;
                return `${股票名称[索引]}<br/>成交额: ${成交额数据[索引]}亿<br/>涨跌幅: ${涨跌幅数据[索引].toFixed(2)}%`;
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            name: '成交额(亿)'
        },
        yAxis: {
            type: 'category',
            data: 股票名称,
            axisLabel: {
                interval: 0,
                fontSize: 10
            }
        },
        series: [{
            name: '成交额',
            type: 'bar',
            data: 成交额数据,
            itemStyle: {
                color: function(params) {
                    return 涨跌幅数据[params.dataIndex] >= 0 ? '#4CAF50' : '#f44336';
                }
            }
        }]
    };
    
    图表实例.成交额图.setOption(配置);
}

// 绘制行业涨跌幅图
function 绘制行业涨跌幅图(行业数据) {
    const 图表容器 = document.getElementById('industry-change-chart');
    
    if (图表实例.行业涨跌图) {
        图表实例.行业涨跌图.dispose();
    }
    
    图表实例.行业涨跌图 = echarts.init(图表容器);
    
    const 行业名称 = 行业数据.map(item => item.industry);
    const 涨跌幅数据 = 行业数据.map(item => item.avgChange.toFixed(2));
    
    const 配置 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            name: '平均涨跌幅(%)',
            axisLine: {
                lineStyle: {
                    color: '#666'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: 行业名称,
            axisLabel: {
                interval: 0,
                fontSize: 10
            }
        },
        series: [{
            name: '平均涨跌幅',
            type: 'bar',
            data: 涨跌幅数据,
            itemStyle: {
                color: function(params) {
                    return params.value >= 0 ? '#4CAF50' : '#f44336';
                }
            }
        }]
    };
    
    图表实例.行业涨跌图.setOption(配置);
}

// 绘制行业成交额图
function 绘制行业成交额图(行业数据) {
    const 图表容器 = document.getElementById('industry-amount-chart');
    
    if (图表实例.行业成交额图) {
        图表实例.行业成交额图.dispose();
    }
    
    图表实例.行业成交额图 = echarts.init(图表容器);
    
    const 数据 = 行业数据.map(item => ({
        name: item.industry,
        value: (item.totalAmount / 100000000).toFixed(2)
    }));
    
    const 配置 = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}亿 ({d}%)'
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 20,
            bottom: 20
        },
        series: [{
            name: '行业成交额',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '18',
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: false
            },
            data: 数据
        }]
    };
    
    图表实例.行业成交额图.setOption(配置);
}

// 切换股票
function 切换股票() {
    const 选择器 = document.getElementById('stock-selector');
    当前股票代码 = 选择器.value;
    
    if (当前股票代码) {
        更新股票图表();
    } else {
        隐藏股票信息();
    }
}

// 更新股票图表
async function 更新股票图表() {
    if (!当前股票代码) return;
    
    显示加载中();
    
    try {
        const 开始日期 = document.getElementById('start-date').value.replace(/-/g, '');
        const 结束日期 = document.getElementById('end-date').value.replace(/-/g, '');
        
        const response = await axios.get(`${API_BASE_URL}/stock/${当前股票代码}/daily`, {
            params: {
                start_date: 开始日期,
                end_date: 结束日期
            }
        });
        
        if (response.data.success) {
            const 数据 = response.data.data;
            
            if (数据.length > 0) {
                显示股票信息(数据);
                绘制K线图(数据);
                绘制成交量图(数据);
            } else {
                显示错误提示('该时间段内无数据');
            }
        } else {
            throw new Error(response.data.error);
        }
        
        隐藏加载中();
        
    } catch (error) {
        console.error('更新股票图表失败:', error);
        显示错误提示('更新股票图表失败');
        隐藏加载中();
    }
}

// 显示股票信息
function 显示股票信息(数据) {
    const 股票信息卡片 = document.getElementById('stock-info');
    const 最新数据 = 数据[数据.length - 1];

    // 获取股票基本信息
    const 股票信息 = 股票列表.find(股票 => 股票.code === 当前股票代码);

    document.getElementById('stock-name').textContent = 股票信息 ? 股票信息.name : 当前股票代码;
    document.getElementById('stock-code').textContent = 当前股票代码;
    document.getElementById('latest-price').textContent = 最新数据.close.toFixed(2);
    document.getElementById('price-change').textContent = 最新数据.pctChange.toFixed(2) + '%';
    document.getElementById('volume').textContent = (最新数据.volume / 10000).toFixed(2) + '万手';

    // 设置涨跌颜色
    const 涨跌幅元素 = document.getElementById('price-change');
    涨跌幅元素.style.color = 最新数据.pctChange >= 0 ? '#4CAF50' : '#f44336';

    股票信息卡片.style.display = 'flex';
}

// 隐藏股票信息
function 隐藏股票信息() {
    document.getElementById('stock-info').style.display = 'none';
}

// 绘制K线图
function 绘制K线图(数据) {
    const 图表容器 = document.getElementById('kline-chart');

    if (图表实例.K线图) {
        图表实例.K线图.dispose();
    }

    图表实例.K线图 = echarts.init(图表容器);

    // 准备数据
    const 日期数据 = 数据.map(item => item.date);
    const K线数据 = 数据.map(item => [item.open, item.close, item.low, item.high]);

    const 配置 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross'
            },
            formatter: function(params) {
                const 数据点 = params[0];
                const 索引 = 数据点.dataIndex;
                const 当日数据 = 数据[索引];

                return `日期: ${当日数据.date}<br/>
                        开盘: ${当日数据.open.toFixed(2)}<br/>
                        收盘: ${当日数据.close.toFixed(2)}<br/>
                        最高: ${当日数据.high.toFixed(2)}<br/>
                        最低: ${当日数据.low.toFixed(2)}<br/>
                        涨跌幅: ${当日数据.pctChange.toFixed(2)}%<br/>
                        成交量: ${(当日数据.volume / 10000).toFixed(2)}万手`;
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'category',
            data: 日期数据,
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            splitNumber: 20,
            min: 'dataMin',
            max: 'dataMax'
        },
        yAxis: {
            scale: true,
            splitArea: {
                show: true
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 50,
                end: 100
            },
            {
                show: true,
                type: 'slider',
                top: '90%',
                start: 50,
                end: 100
            }
        ],
        series: [{
            name: 'K线',
            type: 'candlestick',
            data: K线数据,
            itemStyle: {
                color: '#4CAF50',
                color0: '#f44336',
                borderColor: '#4CAF50',
                borderColor0: '#f44336'
            }
        }]
    };

    图表实例.K线图.setOption(配置);
}

// 绘制成交量图
function 绘制成交量图(数据) {
    const 图表容器 = document.getElementById('volume-chart');

    if (图表实例.成交量图) {
        图表实例.成交量图.dispose();
    }

    图表实例.成交量图 = echarts.init(图表容器);

    const 日期数据 = 数据.map(item => item.date);
    const 成交量数据 = 数据.map(item => item.volume);

    const 配置 = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                const 索引 = params[0].dataIndex;
                const 当日数据 = 数据[索引];
                return `日期: ${当日数据.date}<br/>
                        成交量: ${(当日数据.volume / 10000).toFixed(2)}万手<br/>
                        成交额: ${(当日数据.amount / 100000000).toFixed(2)}亿`;
            }
        },
        grid: {
            left: '10%',
            right: '10%',
            bottom: '15%'
        },
        xAxis: {
            type: 'category',
            data: 日期数据,
            axisLabel: {
                show: false
            }
        },
        yAxis: {
            type: 'value',
            name: '成交量'
        },
        dataZoom: [
            {
                type: 'inside',
                start: 50,
                end: 100
            },
            {
                show: true,
                type: 'slider',
                top: '90%',
                start: 50,
                end: 100
            }
        ],
        series: [{
            name: '成交量',
            type: 'bar',
            data: 成交量数据,
            itemStyle: {
                color: function(params) {
                    const 索引 = params.dataIndex;
                    return 数据[索引].pctChange >= 0 ? '#4CAF50' : '#f44336';
                }
            }
        }]
    };

    图表实例.成交量图.setOption(配置);
}

// 刷新行业分析
async function 刷新行业分析() {
    显示加载中();

    try {
        const response = await axios.get(`${API_BASE_URL}/market/overview`);

        if (response.data.success) {
            const 行业数据 = response.data.data.industries;

            // 绘制行业排行图
            绘制行业排行图(行业数据);
            绘制行业成交量对比图(行业数据);

            // 更新行业表格
            更新行业表格(行业数据);
        } else {
            throw new Error(response.data.error);
        }

        隐藏加载中();

    } catch (error) {
        console.error('刷新行业分析失败:', error);
        显示错误提示('刷新行业分析失败');
        隐藏加载中();
    }
}

// 绘制行业排行图
function 绘制行业排行图(行业数据) {
    const 图表容器 = document.getElementById('industry-ranking-chart');

    if (图表实例.行业排行图) {
        图表实例.行业排行图.dispose();
    }

    图表实例.行业排行图 = echarts.init(图表容器);

    const 行业名称 = 行业数据.map(item => item.industry);
    const 涨跌幅数据 = 行业数据.map(item => item.avgChange.toFixed(2));

    const 配置 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            name: '平均涨跌幅(%)'
        },
        yAxis: {
            type: 'category',
            data: 行业名称,
            axisLabel: {
                interval: 0,
                fontSize: 10
            }
        },
        series: [{
            name: '平均涨跌幅',
            type: 'bar',
            data: 涨跌幅数据,
            itemStyle: {
                color: function(params) {
                    return params.value >= 0 ? '#4CAF50' : '#f44336';
                }
            }
        }]
    };

    图表实例.行业排行图.setOption(配置);
}

// 绘制行业成交量对比图
function 绘制行业成交量对比图(行业数据) {
    const 图表容器 = document.getElementById('industry-volume-chart');

    if (图表实例.行业成交量对比图) {
        图表实例.行业成交量对比图.dispose();
    }

    图表实例.行业成交量对比图 = echarts.init(图表容器);

    const 行业名称 = 行业数据.map(item => item.industry);
    const 成交额数据 = 行业数据.map(item => (item.totalAmount / 100000000).toFixed(2));

    const 配置 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: 行业名称,
            axisLabel: {
                interval: 0,
                rotate: 45,
                fontSize: 10
            }
        },
        yAxis: {
            type: 'value',
            name: '成交额(亿)'
        },
        series: [{
            name: '成交额',
            type: 'bar',
            data: 成交额数据,
            itemStyle: {
                color: '#2196F3'
            }
        }]
    };

    图表实例.行业成交量对比图.setOption(配置);
}

// 更新行业表格
function 更新行业表格(行业数据) {
    const 表格体 = document.querySelector('#industry-table tbody');
    表格体.innerHTML = '';

    行业数据.forEach(行业 => {
        const 行 = document.createElement('tr');
        行.innerHTML = `
            <td>${行业.industry}</td>
            <td>${行业.stockCount}</td>
            <td style="color: ${行业.avgChange >= 0 ? '#4CAF50' : '#f44336'}">${行业.avgChange.toFixed(2)}%</td>
            <td>${(行业.totalAmount / 100000000).toFixed(2)}亿</td>
        `;
        表格体.appendChild(行);
    });
}

// 刷新数据库状态
async function 刷新数据库状态() {
    显示加载中();

    try {
        const response = await axios.get(`${API_BASE_URL}/database/status`);

        if (response.data.success) {
            更新数据库表格(response.data.data);
        } else {
            throw new Error(response.data.error);
        }

        隐藏加载中();

    } catch (error) {
        console.error('刷新数据库状态失败:', error);
        显示错误提示('刷新数据库状态失败');
        隐藏加载中();
    }
}

// 更新数据库表格
function 更新数据库表格(数据库数据) {
    const 表格体 = document.querySelector('#database-table tbody');
    表格体.innerHTML = '';

    Object.entries(数据库数据).forEach(([表名, 信息]) => {
        const 行 = document.createElement('tr');
        行.innerHTML = `
            <td>${表名}</td>
            <td>${信息.recordCount.toLocaleString()}</td>
            <td>${信息.lastUpdate}</td>
            <td>${信息.fieldCount || '-'}</td>
        `;
        表格体.appendChild(行);
    });
}

// 显示加载中
function 显示加载中() {
    document.getElementById('loading').style.display = 'flex';
}

// 隐藏加载中
function 隐藏加载中() {
    document.getElementById('loading').style.display = 'none';
}

// 显示错误提示
function 显示错误提示(消息) {
    const 错误元素 = document.getElementById('error-message');
    const 错误文本 = 错误元素.querySelector('.error-text');

    错误文本.textContent = 消息;
    错误元素.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
        隐藏错误提示();
    }, 3000);
}

// 隐藏错误提示
function 隐藏错误提示() {
    document.getElementById('error-message').style.display = 'none';
}

// 显示成功提示
function 显示成功提示(消息) {
    // 简单的控制台输出，或者可以创建一个成功提示元素
    console.log('✅ ' + 消息);

    // 可以在这里添加成功提示的UI显示逻辑
    // 暂时使用控制台输出
}

// 窗口大小改变时重新调整图表
window.addEventListener('resize', function() {
    Object.values(图表实例).forEach(图表 => {
        if (图表 && typeof 图表.resize === 'function') {
            图表.resize();
        }
    });
});

// 页面卸载时销毁图表实例
window.addEventListener('beforeunload', function() {
    Object.values(图表实例).forEach(图表 => {
        if (图表 && typeof 图表.dispose === 'function') {
            图表.dispose();
        }
    });
});

// ==================== 股票数据总览页面功能 ====================

// 全局变量
let 所有股票数据 = [];
let 筛选后股票数据 = [];
let 当前页码 = 1;
let 每页条数 = 50;
let 排序字段 = '';
let 排序方向 = 'asc';
let 行业列表 = [];
let 可用交易日期 = [];
let 当前交易日期 = '';

// 刷新所有股票数据
async function 刷新所有股票数据() {
    显示股票数据加载中();

    try {
        await 加载所有股票数据();
        显示成功提示('数据刷新成功');
    } catch (error) {
        console.error('刷新股票数据失败:', error);
        显示错误提示('刷新数据失败: ' + error.message);
    } finally {
        隐藏股票数据加载中();
    }
}

// 加载所有股票数据
async function 加载所有股票数据(交易日期 = null) {
    try {
        let url = `${API_BASE_URL}/all-stocks-data`;
        if (交易日期) {
            url += `?trade_date=${交易日期}`;
        }

        console.log('请求URL:', url); // 调试日志

        const response = await axios.get(url);
        console.log('API响应:', response.data); // 调试日志

        if (response.data.success) {
            所有股票数据 = response.data.data;
            筛选后股票数据 = [...所有股票数据];
            当前交易日期 = response.data.query_date;

            console.log('当前交易日期:', 当前交易日期); // 调试日志
            console.log('数据条数:', 所有股票数据.length); // 调试日志

            // 更新统计信息
            更新统计信息(response.data.query_date, response.data.data_count);

            // 提取行业列表
            提取行业列表();

            // 填充行业筛选器
            填充行业筛选器();

            // 重置分页
            当前页码 = 1;

            // 渲染表格
            渲染股票表格();

            // 更新分页
            更新分页控件();

            // 显示滚动提示
            显示滚动提示();

        } else {
            throw new Error(response.data.error);
        }
    } catch (error) {
        console.error('加载股票数据失败:', error);
        显示股票无数据();
        throw error;
    }
}

// 更新统计信息
function 更新统计信息(查询日期, 数据数量) {
    const 总股票数 = 所有股票数据.length;
    const 有数据股票数 = 所有股票数据.filter(股票 => 股票.close !== null).length;

    document.getElementById('total-stocks').textContent = 总股票数.toLocaleString();
    document.getElementById('active-stocks').textContent = 有数据股票数.toLocaleString();
    document.getElementById('latest-date').textContent = 格式化显示日期(查询日期);
}

// 提取行业列表
function 提取行业列表() {
    const 行业集合 = new Set();
    所有股票数据.forEach(股票 => {
        if (股票.industry && 股票.industry !== '-') {
            行业集合.add(股票.industry);
        }
    });
    行业列表 = Array.from(行业集合).sort();
}

// 填充行业筛选器
function 填充行业筛选器() {
    const 行业筛选器 = document.getElementById('industry-filter');
    行业筛选器.innerHTML = '<option value="">所有行业</option>';

    行业列表.forEach(行业 => {
        const option = document.createElement('option');
        option.value = 行业;
        option.textContent = 行业;
        行业筛选器.appendChild(option);
    });
}

// 渲染股票表格
function 渲染股票表格() {
    const 表格体 = document.querySelector('#stocks-data-table tbody');
    表格体.innerHTML = '';

    // 计算分页数据
    const 开始索引 = (当前页码 - 1) * 每页条数;
    const 结束索引 = 开始索引 + 每页条数;
    const 当前页数据 = 筛选后股票数据.slice(开始索引, 结束索引);

    if (当前页数据.length === 0) {
        显示股票无数据();
        return;
    }

    隐藏股票无数据();

    当前页数据.forEach(股票 => {
        const 行 = document.createElement('tr');

        行.innerHTML = `
            <td>${股票.ts_code}</td>
            <td>${股票.symbol || '-'}</td>
            <td>${股票.name}</td>
            <td>${股票.area || '-'}</td>
            <td>${股票.industry || '-'}</td>
            <td>${股票.market || '-'}</td>
            <td>${格式化日期(股票.list_date)}</td>
            <td>${股票.act_name || '-'}</td>
            <td>${股票.act_ent_type || '-'}</td>
            <td>${格式化日期(股票.trade_date)}</td>
            <td>${格式化价格(股票.open)}</td>
            <td>${格式化价格(股票.high)}</td>
            <td>${格式化价格(股票.low)}</td>
            <td>${格式化价格(股票.close)}</td>
            <td>${格式化价格(股票.pre_close)}</td>
            <td class="${获取涨跌样式(股票.change)}">${格式化价格(股票.change)}</td>
            <td class="${获取涨跌样式(股票.pct_chg)}">${格式化百分比(股票.pct_chg)}</td>
            <td>${格式化成交量(股票.vol)}</td>
            <td>${格式化成交额(股票.amount)}</td>
            <td>${格式化倍数(股票.pe)}</td>
            <td>${格式化倍数(股票.pe_ttm)}</td>
            <td>${格式化倍数(股票.pb)}</td>
            <td>${格式化倍数(股票.ps)}</td>
            <td>${格式化倍数(股票.ps_ttm)}</td>
            <td>${格式化百分比(股票.dv_ratio)}</td>
            <td>${格式化百分比(股票.dv_ttm)}</td>
            <td>${格式化市值(股票.total_mv)}</td>
            <td>${格式化市值(股票.circ_mv)}</td>
            <td>${格式化百分比(股票.turnover_rate)}</td>
            <td>${格式化倍数(股票.volume_ratio)}</td>
        `;

        表格体.appendChild(行);
    });
}

// 搜索股票
function 搜索股票() {
    应用筛选();
}

// 应用筛选
function 应用筛选() {
    const 搜索词 = document.getElementById('stock-search').value.toLowerCase().trim();
    const 行业筛选 = document.getElementById('industry-filter').value;
    const 市场筛选 = document.getElementById('market-filter').value;

    筛选后股票数据 = 所有股票数据.filter(股票 => {
        // 搜索筛选
        const 匹配搜索 = !搜索词 ||
            股票.ts_code.toLowerCase().includes(搜索词) ||
            股票.name.toLowerCase().includes(搜索词);

        // 行业筛选
        const 匹配行业 = !行业筛选 || 股票.industry === 行业筛选;

        // 市场筛选
        const 匹配市场 = !市场筛选 || 股票.market === 市场筛选;

        return 匹配搜索 && 匹配行业 && 匹配市场;
    });

    // 重置到第一页
    当前页码 = 1;

    // 重新渲染
    渲染股票表格();
    更新分页控件();
}

// 清除筛选
function 清除筛选() {
    document.getElementById('stock-search').value = '';
    document.getElementById('industry-filter').value = '';
    document.getElementById('market-filter').value = '';

    筛选后股票数据 = [...所有股票数据];
    当前页码 = 1;

    渲染股票表格();
    更新分页控件();
}

// 表格排序
function 表格排序(字段) {
    if (排序字段 === 字段) {
        排序方向 = 排序方向 === 'asc' ? 'desc' : 'asc';
    } else {
        排序字段 = 字段;
        排序方向 = 'asc';
    }

    筛选后股票数据.sort((a, b) => {
        let 值A = a[字段];
        let 值B = b[字段];

        // 处理null值
        if (值A === null || 值A === undefined) 值A = 排序方向 === 'asc' ? Number.NEGATIVE_INFINITY : Number.POSITIVE_INFINITY;
        if (值B === null || 值B === undefined) 值B = 排序方向 === 'asc' ? Number.NEGATIVE_INFINITY : Number.POSITIVE_INFINITY;

        // 数值比较
        if (typeof 值A === 'number' && typeof 值B === 'number') {
            return 排序方向 === 'asc' ? 值A - 值B : 值B - 值A;
        }

        // 字符串比较
        const 字符串A = 值A.toString();
        const 字符串B = 值B.toString();

        if (排序方向 === 'asc') {
            return 字符串A.localeCompare(字符串B);
        } else {
            return 字符串B.localeCompare(字符串A);
        }
    });

    // 更新表头排序指示器
    更新排序指示器();

    // 重新渲染
    渲染股票表格();
}

// 更新排序指示器
function 更新排序指示器() {
    // 清除所有排序指示器
    document.querySelectorAll('.simple-table th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 添加当前排序指示器
    const 当前列 = document.querySelector(`[data-column="${排序字段}"]`);
    if (当前列) {
        当前列.classList.add(排序方向 === 'asc' ? 'sort-asc' : 'sort-desc');
    }
}

// 格式化价格
function 格式化价格(值) {
    if (值 === null || 值 === undefined) return '-';
    const 数值 = parseFloat(值);
    return isNaN(数值) ? '-' : 数值.toFixed(2);
}

// 格式化百分比
function 格式化百分比(值) {
    if (值 === null || 值 === undefined) return '-';
    const 数值 = parseFloat(值);
    return isNaN(数值) ? '-' : 数值.toFixed(2) + '%';
}

// 格式化成交量
function 格式化成交量(值) {
    if (值 === null || 值 === undefined) return '-';
    const 数值 = parseFloat(值);
    if (isNaN(数值)) return '-';

    if (数值 >= 100000000) {
        return (数值 / 100000000).toFixed(2) + '亿手';
    } else if (数值 >= 10000) {
        return (数值 / 10000).toFixed(2) + '万手';
    } else {
        return 数值.toFixed(0) + '手';
    }
}

// 格式化成交额
function 格式化成交额(值) {
    if (值 === null || 值 === undefined) return '-';
    const 数值 = parseFloat(值);
    if (isNaN(数值)) return '-';

    // 值是千元，转换为万元显示
    const 万元 = 数值 / 10;
    if (万元 >= 10000) {
        return (万元 / 10000).toFixed(2) + '亿';
    } else {
        return 万元.toFixed(2) + '万';
    }
}

// 格式化倍数
function 格式化倍数(值) {
    if (值 === null || 值 === undefined) return '-';
    const 数值 = parseFloat(值);
    if (isNaN(数值) || 数值 <= 0) return '-';
    return 数值.toFixed(2);
}

// 格式化市值
function 格式化市值(值) {
    if (值 === null || 值 === undefined) return '-';
    const 数值 = parseFloat(值);
    if (isNaN(数值)) return '-';

    // 值是万元，转换显示
    if (数值 >= 10000) {
        return (数值 / 10000).toFixed(2) + '亿';
    } else {
        return 数值.toFixed(2) + '万';
    }
}

// 获取涨跌样式
function 获取涨跌样式(值) {
    if (值 === null || 值 === undefined) return 'neutral-change';
    const 数值 = parseFloat(值);
    if (isNaN(数值) || 数值 === 0) return 'neutral-change';
    return 数值 > 0 ? 'positive-change' : 'negative-change';
}
// 更新分页控件
function 更新分页控件() {
    const 总记录数 = 筛选后股票数据.length;
    const 总页数 = Math.ceil(总记录数 / 每页条数);

    // 更新分页信息
    const 开始记录 = (当前页码 - 1) * 每页条数 + 1;
    const 结束记录 = Math.min(当前页码 * 每页条数, 总记录数);

    document.getElementById('page-start').textContent = 开始记录;
    document.getElementById('page-end').textContent = 结束记录;
    document.getElementById('total-records').textContent = 总记录数;

    // 更新分页按钮状态
    document.getElementById('prev-page').disabled = 当前页码 <= 1;
    document.getElementById('next-page').disabled = 当前页码 >= 总页数;

    // 生成页码按钮
    生成页码按钮(总页数);
}

// 生成页码按钮
function 生成页码按钮(总页数) {
    const 页码容器 = document.getElementById('page-numbers');
    页码容器.innerHTML = '';

    if (总页数 <= 1) return;

    const 最大显示页码 = 5;
    let 开始页码 = Math.max(1, 当前页码 - Math.floor(最大显示页码 / 2));
    let 结束页码 = Math.min(总页数, 开始页码 + 最大显示页码 - 1);

    // 调整开始页码
    if (结束页码 - 开始页码 + 1 < 最大显示页码) {
        开始页码 = Math.max(1, 结束页码 - 最大显示页码 + 1);
    }

    for (let i = 开始页码; i <= 结束页码; i++) {
        const 页码按钮 = document.createElement('span');
        页码按钮.className = 'page-number';
        页码按钮.textContent = i;

        if (i === 当前页码) {
            页码按钮.classList.add('active');
        }

        页码按钮.onclick = () => 跳转到页面(i);
        页码容器.appendChild(页码按钮);
    }
}

// 跳转到页面
function 跳转到页面(页码) {
    当前页码 = 页码;
    渲染股票表格();
    更新分页控件();
}

// 上一页
function 上一页() {
    if (当前页码 > 1) {
        跳转到页面(当前页码 - 1);
    }
}

// 下一页
function 下一页() {
    const 总页数 = Math.ceil(筛选后股票数据.length / 每页条数);
    if (当前页码 < 总页数) {
        跳转到页面(当前页码 + 1);
    }
}

// 更改页面大小
function 更改页面大小() {
    每页条数 = parseInt(document.getElementById('page-size').value);
    当前页码 = 1;
    渲染股票表格();
    更新分页控件();
}

// 显示股票数据加载中
function 显示股票数据加载中() {
    document.getElementById('stocks-loading').style.display = 'block';
    document.querySelector('.stocks-table-container').style.display = 'none';
    document.getElementById('stocks-no-data').style.display = 'none';
}

// 隐藏股票数据加载中
function 隐藏股票数据加载中() {
    document.getElementById('stocks-loading').style.display = 'none';
    document.querySelector('.table-container').style.display = 'block';
}

// 显示股票无数据
function 显示股票无数据() {
    document.getElementById('stocks-no-data').style.display = 'block';
    document.querySelector('.table-container').style.display = 'none';
}

// 隐藏股票无数据
function 隐藏股票无数据() {
    document.getElementById('stocks-no-data').style.display = 'none';
    document.querySelector('.table-container').style.display = 'block';
}

// 格式化显示日期
function 格式化显示日期(日期字符串) {
    if (!日期字符串) return '-';

    const 日期 = 日期字符串.toString();
    if (日期.length === 8) {
        return `${日期.substr(0, 4)}-${日期.substr(4, 2)}-${日期.substr(6, 2)}`;
    }
    return 日期;
}

// 格式化日期（用于表格显示）
function 格式化日期(日期字符串) {
    if (!日期字符串 || 日期字符串 === '-') return '-';
    return 格式化显示日期(日期字符串);
}

// 加载可用交易日期
async function 加载可用交易日期() {
    try {
        const response = await axios.get(`${API_BASE_URL}/available-dates`);
        if (response.data.success) {
            可用交易日期 = response.data.dates;
            填充日期选择器();
        }
    } catch (error) {
        console.error('加载可用交易日期失败:', error);
    }
}

// 填充日期选择器
function 填充日期选择器() {
    const 日期选择器 = document.getElementById('trade-date-select');
    日期选择器.innerHTML = '<option value="">最新交易日</option>';

    可用交易日期.forEach(日期 => {
        const option = document.createElement('option');
        option.value = 日期;
        option.textContent = 格式化显示日期(日期);
        日期选择器.appendChild(option);
    });

    console.log('可用交易日期:', 可用交易日期); // 调试日志
}

// 切换交易日期
async function 切换交易日期() {
    const 选择的日期 = document.getElementById('trade-date-select').value;

    console.log('选择的日期:', 选择的日期); // 调试日志

    显示股票数据加载中();

    try {
        await 加载所有股票数据(选择的日期 || null);

        // 重新应用当前的筛选条件
        应用筛选();

        显示成功提示('数据加载成功');
    } catch (error) {
        console.error('切换交易日期失败:', error);
        显示错误提示('切换日期失败: ' + error.message);
    } finally {
        隐藏股票数据加载中();
    }
}

// 显示滚动提示
function 显示滚动提示() {
    const 提示元素 = document.getElementById('scroll-hint');
    if (提示元素) {
        提示元素.style.display = 'block';
        // 3秒后自动隐藏
        setTimeout(() => {
            提示元素.style.display = 'none';
        }, 3000);
    }
}
